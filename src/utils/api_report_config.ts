import { ProjectConfigCreate, ProjectLeader, ProjectMember } from '@/types/ReportConfig';
import api, { downloadFile } from './api';

export const path = {
  project_leaders: '/project-leaders',
  project_members: '/project-members',
  project_member_joins: '/project-member-joins',
  project_configs: '/project-configs',
  requirements_attachments_files: '/requirements-attachments-files',
  project_configs_paginated: '/project-configs/list/paginated',
  project_url: '/project-url',
  ai_traces: '/ai-traces'
}

// 项目主体相关API
export const projectLeaderApi = {
  // 获取项目主体列表
  getProjectLeaders: (): Promise<any> =>
    api.get(`${path.project_leaders}/list`),

  // 获取指定项目主体详情
  getProjectLeaderById: (leaderId: string): Promise<any> =>
    api.get(`${path.project_leaders}/${leaderId}`),

  // 创建项目主体
  createProjectLeader: (data: ProjectLeader): Promise<any> =>
    api.post(path.project_leaders, data),

  // 更新项目主体
  updateProjectLeader: (leaderId: string, data: ProjectLeader): Promise<any> =>
    api.put(`${path.project_leaders}/${leaderId}`, data),

  // 软删除项目主体
  deleteProjectLeader: (leaderId: string): Promise<any> =>
    api.delete(`${path.project_leaders}/${leaderId}`),

  // 生成材料主体AI介绍--传参没用，默认模型
  generateAiLeader: (leader_id: string, name: string, model_config_id: string): Promise<any> =>
    api.post(`${path.project_configs}/generate-ai-leader`, {leader_id, name, model_config_id}),
};

// 团队成员相关API
export const projectMemberApi = {
  // 获取全部成员列表
  getProjectMembers: (): Promise<any> =>
    api.get(`${path.project_members}/list`),

  // 获取指定成员详情
  getProjectMemberById: (memberId: string): Promise<any> =>
    api.get(`${path.project_members}/${memberId}`),

  // 创建成员
  createProjectMember: (data: ProjectMember): Promise<any> =>
    api.post(path.project_members, data),

  // 更新成员
  updateProjectMember: (memberId: string, data: ProjectMember): Promise<any> =>
    api.put(`${path.project_members}/${memberId}`, data),

  // 删除成员
  deleteProjectMember: (memberId: string): Promise<any> =>
    api.delete(`${path.project_members}/${memberId}`),

  // 生成团队成员介绍--传参没用，默认模型
  getProjectMemberIntro: (member_ids: string[], project_configs_name: string, model_config_id: string): Promise<any> =>
    api.post(`${path.project_members}/introduction`, { member_ids, project_configs_name, model_config_id }),
};

// 项目成员关联相关API
export const projectMemberJoinApi = {
  // 获取所有项目成员关联列表
  getProjectMemberJoins: (): Promise<any> =>
    api.get(`${path.project_member_joins}/list`),

  // 获取指定关联ID的项目成员关联
  getProjectMemberJoinById: (joinId: string): Promise<any> =>
    api.get(`${path.project_member_joins}/${joinId}`),

  // 创建项目成员关联
  createProjectMemberJoin: (data: any): Promise<any> =>
    api.post(path.project_member_joins, data),
};

// AI去痕
export const aiTraceApi = {
  // AI去痕上传
  uploadAiTraces: (file: any): Promise<any> =>
    api.post(`${path.ai_traces}/traces`, file, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // AI去痕优化内容下载
  downloadAiTraces: (trace_id: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}${path.ai_traces}/export-traces?trace_id=${trace_id}`;
    return downloadFile(
      url,
      filename,
      'application/octet-stream',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
  },

  // AI去痕优化内容下载PDF
  downloadAiTracesPdf: (trace_id: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}${path.ai_traces}/export-traces-pdf?trace_id=${trace_id}`;
    return downloadFile(
      url,
      filename,
      'application/pdf',
      'application/pdf'
    );
  }

};

// 参考资料相关API
export const projectFileApi = {
  // 参考资料上传
  uploadAttachments: (file: any, project_configs_name: string): Promise<any> =>
    api.post(`${path.requirements_attachments_files}/process?project_configs_name=${project_configs_name}`, file, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // 参考资料附件删除
  deleteAttachments: (attachment_id: string): Promise<any> =>
    api.delete(`${path.requirements_attachments_files}/${attachment_id}`),

  // 参考资料附件列表
  getAttachmentsList: (project_id: string): Promise<any> =>
    api.get(`${path.requirements_attachments_files}`, { params: { project_id } }),

  /** 参考资料总结 */
  refreshAttachmentsSummary: (data: any): Promise<any> =>
    api.post(`${path.requirements_attachments_files}/batch-resummarize`, data),
};

// 参考文献网址
export const referenceUrlApi = {
  // 批量追加url，并异步核验
  batchInsertUrl: (data: { url: string[], name: string }): Promise<any> =>
    api.post(`${path.project_url}/batch`, data),

  // 根据ID获取所有链接信息
  getUrlById: (ids: string[]): Promise<any> =>
    api.post(`${path.project_url}/by-id`, { ids }),

  // 文献网址列表
  getUrlList: (project_id: string): Promise<any> =>
    api.get(`${path.project_url}/by-project`, { params: { project_id } }),

  // 检测URL异步任务是否在运行
  checkUrlTask: (ids: string[]): Promise<any> =>
    api.post(`${path.project_url}/detect-async`, { ids }),

  // 停止URL异步任务
  stopUrlTask: (ids: string[]): Promise<any> =>
    api.post(`${path.project_url}/stop-async`, { ids }),
};

// 自定义模版
export const customTemplateApi = {
  // 上传模版
  uploadTemplate: (file: any): Promise<any> =>
    api.post(`${path.project_configs}/upload-outline`, file, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // 获取模版文件
  getTemplateFile: (fileId: string): Promise<any> =>
    api.get(`/upload-file/content?file_id=${fileId}`),
};

// 项目配置相关API
export const projectConfigApi = {
  // 获取项目配置列表
  getProjectList: (): Promise<any> =>
    api.get(`${path.project_configs}/list`),

  // 获取分页的项目配置列表
  getProjectListPaginated: (page: number = 1, size: number = 10): Promise<any> =>
    api.get(`${path.project_configs_paginated}`, {
      params: { page, size }
    }),

  // 获取指定项目配置详情
  getProjectConfigById: (projectId: string): Promise<any> =>
    api.get(`${path.project_configs}/${projectId}`),

  // 优化报告主题课题--传参没用，默认模型
  optimizeReportTitle: (project_configs_name: string, model_config_id: string): Promise<any> =>
    api.post(`${path.project_configs}/optimize-name`, { project_configs_name, model_config_id }),

  // 创建项目配置
  createProjectConfig: (data: ProjectConfigCreate): Promise<any> =>
    api.post(path.project_configs, data),

  // 选择模型与项目配置关联——直接传参不用关联了
  // selectModelConfig: (model_config_id: string, project_config_id: string): Promise<any> =>
  //   api.post(`/project-model-configs/`, { model_config_id, project_config_id }),

  // 更新项目配置
  updateProjectConfig: (projectId: string, data: ProjectConfigCreate): Promise<any> =>
    api.put(`${path.project_configs}/${projectId}`, data),

  // 软删除项目配置
  deleteProjectConfig: (projectId: string): Promise<any> =>
    api.delete(`${path.project_configs}/${projectId}`),

  // 更新大纲
  updateManualOutline: (projectId: string, data: any): Promise<any> =>
    api.post(`${path.project_configs}/${projectId}/update-manual-outline`, data),

  // 更新报告
  updateReport: (projectId: string, data: any): Promise<any> =>
    api.post(`${path.project_configs}/${projectId}/update-report`, data),

  // 参考文献库
  getLiteratureLibrary: (): Promise<any> =>
    api.get(`${path.project_configs}/dict/literature-library`),
};

export default {
  projectLeaderApi,
  projectMemberApi,
  projectMemberJoinApi,
  projectConfigApi
};

