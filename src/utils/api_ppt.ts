import api from './api';

// PPT相关API路径配置
export const path = {
  ppt: '/api/ppt'
};

// PPT生成请求参数接口
export interface PptGenerateParams {
  file: File;
  ppt_type: string;
}

// PPT生成响应接口
export interface PptGenerateResponse {
  success: boolean;
  code: number;
  data: {
    file_path: string;      // PPT文件路径
    filename: string;       // 文件名
    size: number;          // 文件大小（字节）
    message: string;       // 生成结果消息
    document_content: string; // 文档内容预览（前500字符）
  };
  error?: string;
}

// PPT模板接口
export interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// PPT相关API
export const pptApi = {
  /**
   * 生成PPT
   * @param formData 包含文件和模板类型的FormData
   * @returns Promise<PPT生成结果>
   */
  generatePpt: (formData: FormData): Promise<PptGenerateResponse['data']> =>
    api.post(`${path.ppt}/generate`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  /**
   * 获取PPT模板列表
   * TODO: 后端接口开发完成后启用
   * @returns Promise<PPT模板列表>
   */
  getPptTemplates: (): Promise<PptTemplate[]> =>
    api.get(`${path.ppt}/templates`),

  /**
   * 根据ID获取PPT模板详情
   * TODO: 后端接口开发完成后启用
   * @param templateId 模板ID
   * @returns Promise<PPT模板详情>
   */
  getPptTemplateById: (templateId: string): Promise<PptTemplate> =>
    api.get(`${path.ppt}/templates/${templateId}`),

  /**
   * 下载生成的PPT文件
   * TODO: 后续实现下载功能
   * @param filePath PPT文件路径
   * @param filename 下载文件名
   * @returns Promise<void>
   */
  downloadPpt: (filePath: string, filename: string): Promise<void> => {
    const url = `${api.defaults.baseURL}${path.ppt}/download?file_path=${encodeURIComponent(filePath)}`;
    return new Promise((resolve, reject) => {
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取PPT生成状态
   * TODO: 如果需要支持异步生成，可以添加此接口
   * @param taskId 任务ID
   * @returns Promise<生成状态>
   */
  getPptGenerateStatus: (taskId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    message: string;
    result?: PptGenerateResponse['data'];
  }> =>
    api.get(`${path.ppt}/status/${taskId}`),
};

export default { pptApi };
