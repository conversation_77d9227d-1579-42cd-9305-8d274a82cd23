import {
  ApiOutlined,
  AppstoreOutlined,
  BankOutlined,
  DashboardOutlined,
  FileOutlined,
  HomeOutlined,
  MessageOutlined,
  SettingOutlined,
  TeamOutlined,
  SafetyOutlined,
  FileTextOutlined,
  BookOutlined,
  LineChartOutlined
} from "@ant-design/icons";
import { MenuItemType } from "antd/es/menu/interface";

export interface CustomMenuItem extends MenuItemType {
  path: string;
  children?: CustomMenuItem[];
}

// 创建一个函数来获取菜单配置
export const getMenuConfig = (isSuperAdmin: boolean, isOrgAdmin: boolean): CustomMenuItem[] => {
  // 所有用户都能看到的基础菜单
  const baseMenus: CustomMenuItem[] = [
    {
      label: "高校AI助手",
      title: "College-Agent",
      key: "college",
      icon: <HomeOutlined />,
      path: "/college",
      children: [
        {
          label: "首页",
          title: "首页",
          key: "home",
          icon: <HomeOutlined style={{ color: "#4285F4" }} />,
          path: "/home",
        },
        {
          label: "智能聊天",
          title: "智能聊天",
          key: "chat",
          icon: <MessageOutlined style={{ color: "#4CAF50" }} />,
          path: "/chat",
        },
        {
          label: "作业辅导",
          title: "作业辅导",
          key: "homework",
          icon: <BookOutlined style={{ color: "#673AB7" }} />,
          path: "/homework",
        },
        {
          label: "论文写作",
          title: "论文写作",
          key: "paper",
          icon: <FileTextOutlined style={{ color: "#2196F3" }} />,
          path: "/paper",
        },
        {
          label: "PPT生成",
          title: "PPT生成",
          key: "ppt",
          icon: <FileOutlined style={{ color: "#F44336" }} />,
          path: "/ppt",
        },
        {
          label: "降低AI",
          title: "降低AI",
          key: "ai-traces",
          icon: <SafetyOutlined style={{ color: "#FF9800" }} />,
          path: "/ai-traces",
        },
        {
          label: "学习轨迹",
          title: "学习轨迹",
          key: "learning",
          icon: <LineChartOutlined style={{ color: "#673AB7" }} />,
          path: "/learning",
        }
      ],
    },
  ];

  // 超级管理员可以看到的后台管理菜单
  const superAdminBackgroundMenu: CustomMenuItem = {
    label: "后台管理",
    title: "后台管理系统",
    key: "background",
    icon: <DashboardOutlined />,
    path: "/background",
    children: [
      {
        label: "菜单管理",
        title: "菜单管理",
        key: "menu",
        icon: <AppstoreOutlined style={{ color: "#FF9800" }} />,
        path: "/menu",
      },
      {
        label: "机构管理",
        title: "机构管理",
        key: "organization",
        icon: <BankOutlined style={{ color: "#F44336" }} />,
        path: "/organization",
      },
      {
        label: "用户管理",
        title: "用户管理",
        key: "user",
        icon: <TeamOutlined style={{ color: "#673AB7" }} />,
        path: "/user",
      },
      {
        label: "模型管理",
        title: "模型管理",
        key: "model",
        icon: <ApiOutlined style={{ color: "#4CAF50" }} />,
        path: "/model",
      },
      // {
      //   label: "角色管理",
      //   title: "角色管理",
      //   key: "role",
      //   icon: <SettingOutlined />,
      //   path: "/role",
      // },
    ],
  };

  // 机构管理员可以看到的后台管理菜单
  const orgAdminBackgroundMenu: CustomMenuItem = {
    label: "后台管理",
    title: "后台管理系统",
    key: "background",
    icon: <DashboardOutlined />,
    path: "/background",
    children: [
      {
        label: "用户管理",
        title: "用户管理",
        key: "user",
        icon: <SettingOutlined style={{ color: "#673AB7" }} />,
        path: "/user",
      },
      {
        label: "角色管理",
        title: "角色管理",
        key: "role",
        icon: <SettingOutlined style={{ color: "#4CAF50" }} />,
        path: "/role",
      },
    ],
  };

  // 根据角色返回对应的菜单配置
  if (isSuperAdmin) {
    // 超级管理员可以看到所有菜单
    return [...baseMenus, superAdminBackgroundMenu];
  } else if (isOrgAdmin) {
    // 机构管理员可以看到基础菜单和部分后台管理菜单
    return [...baseMenus, orgAdminBackgroundMenu];
  } else {
    // 普通用户只能看到基础菜单
    return baseMenus;
  }
};

// 默认导出一个空的菜单配置，需要在组件中调用 getMenuConfig 获取实际配置
export const menu: CustomMenuItem[] = [];
