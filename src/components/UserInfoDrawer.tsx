import { useAuth } from '@/contexts/AuthContext';
import { UserInfo } from '@/types/UserInfo';
import { decrypt } from "@/utils/crypto";
import { UserOutlined } from '@ant-design/icons';
import { Button, Descriptions, Drawer, Spin, Typography, Progress } from 'antd';
import React, { useEffect, useState } from 'react';

const { Title } = Typography;

interface UserInfoDrawerProps {
  visible: boolean;
  onClose: () => void;
}

const UserInfoDrawer: React.FC<UserInfoDrawerProps> = ({ visible, onClose }) => {
  const [loading, setLoading] = useState(false);
  const { userInfo, logout, updateUserInfo } = useAuth();  // 从 AuthContext 获取用户信息

  useEffect(() => {
    if (visible) {
      // 如果需要刷新用户信息，可以调用 updateUserInfo
      if (!userInfo) {
        setLoading(true);
        updateUserInfo().finally(() => {
          setLoading(false);
        });
      }
    }
  }, [visible, userInfo]);

  // 格式化时间
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Button type="primary" shape="circle" icon={<UserOutlined />} />
          <Title level={4} style={{ margin: 0 }}>
            用户信息
          </Title>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={400}
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>
          <Spin size="large" />
        </div>
      ) : userInfo ? (
        <Descriptions bordered column={1}>
          <Descriptions.Item label="用户名">{userInfo.username || '-'}</Descriptions.Item>
          <Descriptions.Item label="真实姓名">{userInfo.realname || '-'}</Descriptions.Item>
          <Descriptions.Item label="手机号">{decrypt(userInfo.mobile) || '-'}</Descriptions.Item>
          <Descriptions.Item label="所属机构">{userInfo.organization?.name || '-'}</Descriptions.Item>
          <Descriptions.Item label="单位">{userInfo.company || '-'}</Descriptions.Item>
          <Descriptions.Item label="职称">{userInfo.position || '-'}</Descriptions.Item>
          <Descriptions.Item label="成就">{userInfo.achievement || '-'}</Descriptions.Item>
          <Descriptions.Item label="角色">{userInfo.role.name}</Descriptions.Item>
          {userInfo.used_count !== undefined && userInfo.max_allowed_count !== undefined && (
            <Descriptions.Item label="使用次数">
              <div>
                <Progress 
                  percent={Math.min(100, (userInfo.used_count / userInfo.max_allowed_count) * 100)}
                  // percent={userInfo.max_allowed_count === null ? 0 : Math.min(100, (userInfo.used_count / userInfo.max_allowed_count) * 100)} 
                  size="small"
                  format={() => userInfo.max_allowed_count === null ? `${userInfo.used_count} / 无限次` : `${userInfo.used_count} / ${userInfo.max_allowed_count}`}
                  // format={() => userInfo.max_allowed_count === null ? `${userInfo.used_count} / 无限次` : `${userInfo.used_count} / ${userInfo.max_allowed_count}`}
                  status={userInfo.used_count >= userInfo.max_allowed_count ? 'exception' : 'active'}
                  // status={userInfo.max_allowed_count !== null && userInfo.used_count >= userInfo.max_allowed_count ? 'exception' : 'active'}
                  strokeColor={{
                    '0%': '#7b61ff',
                    '100%': userInfo.used_count >= userInfo.max_allowed_count ? '#7b61ff' : '#ff4d4f',
                    // '100%': userInfo.max_allowed_count !== null && userInfo.used_count >= userInfo.max_allowed_count ? '#ff4d4f' : '#7b61ff',
                  }}
                />
              </div>
            </Descriptions.Item>
          )}
          {/* <Descriptions.Item label="创建时间">{formatDate(userInfo.created_at)}</Descriptions.Item> */}
          {/* <Descriptions.Item label="更新时间">{formatDate(userInfo.updated_at)}</Descriptions.Item> */}
          {/* <Descriptions.Item label="是否删除">{userInfo.is_deleted ? '是' : '否'}</Descriptions.Item> */}
        </Descriptions>
      ) : (
        <div style={{ textAlign: 'center', padding: '20px 0', color: '#999' }}>
          未获取到用户信息
        </div>
      )}
    </Drawer>
  );
};

export default UserInfoDrawer; 