import React from 'react';
import { Upload } from 'antd';
import { LoadingOutlined, SafetyOutlined } from '@ant-design/icons';
import styles from './index.module.css';

interface FileUploadDraggerProps {
  /** 上传区域标题 */
  title: string;
  /** 上传区域描述 */
  description: string | React.ReactNode;
  /** 接受的文件类型 */
  accept: string;
  /** 文件上传处理函数 */
  onUpload: (file: File, fileList: File[]) => boolean | void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否正在上传 */
  isUploading?: boolean;
  /** 上传中的提示文字 */
  uploadingText?: string;
  /** 是否支持多文件上传 */
  multiple?: boolean;
}

/**
 * 统一的文件上传拖拽组件
 * 基于"降低AI"页面的视觉设计，提供统一的上传体验
 */
const FileUploadDragger: React.FC<FileUploadDraggerProps> = ({
  title,
  description,
  accept,
  onUpload,
  disabled = false,
  isUploading = false,
  uploadingText = "正在上传文件，请稍候...",
  multiple = false,
}) => {
  return (
    <div className={styles.uploaderContainer}>
      {/* 上传区域信息 */}
      <div className={styles.uploaderInfo}>
        <div className={styles.uploaderTitle}>{title}</div>
        <div className={styles.uploaderDesc}>{description}</div>
      </div>

      {/* 文件拖拽上传区域 */}
      <div className={styles.uploadDragger}>
        <Upload.Dragger
          accept={accept}
          beforeUpload={(file, fileList) => {
            return onUpload(file, fileList);
          }}
          showUploadList={false}
          multiple={multiple}
          disabled={disabled || isUploading}
        >
          <div className={styles.dragInner}>
            <SafetyOutlined className={styles.dragIcon} />
            <div className={styles.dragText}>拖拽文件到此处或点击上传</div>
          </div>
        </Upload.Dragger>
      </div>

      {/* 上传状态显示 */}
      {isUploading && (
        <div className={styles.uploadingInfo}>
          <LoadingOutlined />
          <div className={styles.uploadingText}>{uploadingText}</div>
        </div>
      )}
    </div>
  );
};

export default FileUploadDragger;
