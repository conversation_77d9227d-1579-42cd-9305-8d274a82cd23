/* 文件上传拖拽组件样式 - 基于"降低AI"页面的设计 */

/* 上传容器 */
.uploaderContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: fit-content;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

/* 上传信息区域 */
.uploaderInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  min-height: fit-content;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.uploaderTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.uploaderDesc {
  font-size: 14px;
  color: #555;
}

/* 拖拽上传区域 */
.uploadDragger {
  margin-bottom: 16px;
}

.dragInner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 16px 0;
}

.dragText {
  font-size: 16px;
  color: #333;
}

/* 关键样式：橙色背景的安全图标 */
.dragIcon {
  font-size: 32px;
  font-weight: bold;
  padding: 10px;
  border-radius: 16px;
  color: #fff;
  background-color: #EC511F;
}

/* 上传状态显示 */
.uploadingInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 16px 0px;
}

.uploadingText {
  margin-left: 8px;
  font-size: 14px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .uploaderContainer {
    padding: 16px;
  }
  
  .dragIcon {
    font-size: 28px;
    padding: 8px;
  }
  
  .dragText {
    font-size: 14px;
  }
}
