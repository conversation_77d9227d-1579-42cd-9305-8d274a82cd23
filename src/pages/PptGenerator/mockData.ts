/**
 * PPT模板Mock数据
 * 注意：这是临时的Mock数据，用于前端开发和测试
 * 后续需要替换为真实的后端API接口
 */

// PPT模板接口定义
export interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// Mock PPT模板数据
export const mockPptTemplates: PptTemplate[] = [
  {
    id: "academic-classic",
    name: "学术经典模板",
    description: "适用于学术论文展示，包含封面、目录、引言、方法、结果、讨论、结论等标准章节",
    thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIzMCIgZmlsbD0iIzE4OTBGRiIvPgo8dGV4dCB4PSIxMDAiIHk9IjMwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5a2m5pyv57uP5YW4PC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iNTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iI0Y5RjlGOSIgc3Ryb2tlPSIjRTVFNUU1Ii8+CjxyZWN0IHg9IjEwMCIgeT0iNTAiIHdpZHRoPSI5MCIgaGVpZ2h0PSIxNSIgZmlsbD0iI0U1RTVFNSIvPgo8cmVjdCB4PSIxMDAiIHk9IjcwIiB3aWR0aD0iOTAiIGhlaWdodD0iMTUiIGZpbGw9IiNFNUU1RTUiLz4KPHJlY3QgeD0iMTAwIiB5PSI5MCIgd2lkdGg9IjkwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjRTVFNUU1Ii8+CjxyZWN0IHg9IjEwMCIgeT0iMTEwIiB3aWR0aD0iOTAiIGhlaWdodD0iMTUiIGZpbGw9IiNFNUU1RTUiLz4KPC9zdmc+",
    type: "academic_classic"
  },
  {
    id: "research-modern",
    name: "现代研究模板",
    description: "现代化设计风格，适用于科研成果展示，突出数据可视化和图表展示",
    thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRkFGQUZBIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIyNSIgZmlsbD0iIzUyQzQxQSIvPgo8dGV4dCB4PSIxMDAiIHk9IjI4IiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+546w5Luj56CU56m25qih5p2/PC90ZXh0Pgo8Y2lyY2xlIGN4PSI1MCIgY3k9IjgwIiByPSIyNSIgZmlsbD0iIzUyQzQxQSIgb3BhY2l0eT0iMC4yIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSIyMCIgZmlsbD0iIzUyQzQxQSIgb3BhY2l0eT0iMC40Ii8+CjxjaXJjbGUgY3g9IjE1MCIgY3k9IjgwIiByPSIzMCIgZmlsbD0iIzUyQzQxQSIgb3BhY2l0eT0iMC4zIi8+CjxyZWN0IHg9IjEwIiB5PSIxMjAiIHdpZHRoPSIxODAiIGhlaWdodD0iMjAiIGZpbGw9IiNGMEYwRjAiLz4KPC9zdmc+",
    type: "research_modern"
  },
  {
    id: "business-professional",
    name: "商务专业模板",
    description: "商务风格设计，适用于项目汇报、商业计划书等正式场合的演示",
    thumbnail: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMDAiIGhlaWdodD0iNDAiIGZpbGw9IiMyRjU0RUIiLz4KPHR5cGUgeD0iMTAwIiB5PSIyNSIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuWVhuWKoeS4k+S4muaooeadvzwvdGV4dD4KPHJlY3QgeD0iMjAiIHk9IjYwIiB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIGZpbGw9IiNGNUY1RjUiIHN0cm9rZT0iI0U1RTVFNSIvPgo8cmVjdCB4PSI5MCIgeT0iNjAiIHdpZHRoPSI5MCIgaGVpZ2h0PSI0MCIgZmlsbD0iI0Y1RjVGNSIgc3Ryb2tlPSIjRTVFNUU1Ii8+CjxyZWN0IHg9IjIwIiB5PSIxMTAiIHdpZHRoPSIxNjAiIGhlaWdodD0iMjAiIGZpbGw9IiNGMEYwRjAiLz4KPC9zdmc+",
    type: "business_professional"
  }
];

/**
 * 获取PPT模板列表
 * TODO: 后续替换为真实的API调用
 */
export const getPptTemplates = (): Promise<PptTemplate[]> => {
  return new Promise((resolve) => {
    // 模拟API延迟
    setTimeout(() => {
      resolve(mockPptTemplates);
    }, 300);
  });
};

/**
 * 根据ID获取PPT模板详情
 * TODO: 后续替换为真实的API调用
 */
export const getPptTemplateById = (id: string): Promise<PptTemplate | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const template = mockPptTemplates.find(t => t.id === id);
      resolve(template || null);
    }, 200);
  });
};
