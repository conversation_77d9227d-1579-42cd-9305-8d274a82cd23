import React, { useState } from 'react';
import { App, <PERSON><PERSON>, <PERSON>, Col, Row } from 'antd';
import { 
  DownloadOutlined, 
  FileTextOutlined, 
  LoadingOutlined,
  PlayCircleOutlined,
  SyncOutlined 
} from '@ant-design/icons';
import FileUploadDragger from '@/components/FileUploadDragger';
import { pptApi } from '@/utils/api_ppt';
import { mockPptTemplates } from './mockData';
import styles from './PptGenerator.module.css';

// PPT模板接口
interface PptTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: string;
}

// 上传文件接口
interface UploadedFile {
  file: File;
  name: string;
  size: number;
}

// PPT生成结果接口
interface PptGenerateResult {
  file_path: string;
  filename: string;
  size: number;
  message: string;
  document_content: string;
}

const PptGenerator: React.FC = () => {
  const { message } = App.useApp();
  
  // 状态管理
  const [isUploading, setIsUploading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<PptTemplate | null>(null);
  const [generateResult, setGenerateResult] = useState<PptGenerateResult | null>(null);

  /** 处理文件上传 */
  const handleFileUpload = (file: File, fileList: File[]) => {
    if (isUploading) {
      message.warning("文件上传中，请稍后再试");
      return false;
    }
    
    if (isGenerating) {
      message.warning("PPT生成中，请稍后再试");
      return false;
    }

    // 文件大小检查（假设2万字约等于40MB，这里简单检查文件大小）
    const maxSize = 40 * 1024 * 1024; // 40MB
    if (file.size > maxSize) {
      message.error("文件过大，请确保文档内容不超过2万字");
      return false;
    }

    // 文件名长度检查
    if (file.name.length > 100) {
      message.error("文件名过长，请修改后重新上传（100字符以内）");
      return false;
    }

    console.log("准备上传PPT文档文件:", file.name);
    
    // 设置上传的文件信息
    setUploadedFile({
      file: file,
      name: file.name,
      size: file.size
    });
    
    message.success(`${file.name} 上传成功`);
    
    // 清除之前的生成结果
    setGenerateResult(null);
    
    return false; // 阻止默认上传行为
  };

  /** 选择PPT模板 */
  const handleTemplateSelect = (template: PptTemplate) => {
    setSelectedTemplate(template);
    message.success(`已选择模板：${template.name}`);
  };

  /** 生成PPT */
  const handleGeneratePpt = async () => {
    if (!uploadedFile) {
      message.error("请先上传文档文件");
      return;
    }
    
    if (!selectedTemplate) {
      message.error("请先选择PPT模板");
      return;
    }

    try {
      setIsGenerating(true);
      console.log("开始生成PPT:", uploadedFile.name, selectedTemplate.type);
      
      const formData = new FormData();
      formData.append("file", uploadedFile.file);
      formData.append("ppt_type", selectedTemplate.type);
      
      const result = await pptApi.generatePpt(formData);
      console.log("PPT生成成功:", result);
      
      setGenerateResult(result);
      message.success("PPT生成成功！");
      
    } catch (error) {
      console.error("PPT生成失败:", error);
      message.error("PPT生成失败，请稍后重试");
    } finally {
      setIsGenerating(false);
    }
  };

  /** 重新生成 */
  const handleRegenerate = () => {
    setGenerateResult(null);
    handleGeneratePpt();
  };

  /** 下载PPT */
  const handleDownload = () => {
    if (!generateResult) {
      message.error("暂无可下载的PPT文件");
      return;
    }
    
    // TODO: 实现下载功能
    message.info("下载功能将在后续版本中实现");
  };

  return (
    <div className={styles.pptContainer}>
      {/* 页面标题 */}
      <div className={styles.pptTitle}>
        <div className={styles.titleLeft}>
          <div className={styles.titleText}>AI PPT生成器</div>
          <div className={styles.titleDesc}>智能文档转PPT，快速生成专业演示文稿</div>
        </div>
      </div>

      <div className={styles.pptContent}>
        {/* 左侧：文件上传和模板选择 */}
        <div className={styles.pptLeft}>
          {/* 文件上传区域 */}
          <FileUploadDragger
            title="上传文档文件"
            description={
              <>
                支持 Word文档（DOC、DOCX）、PDF文件、文本文件（TXT）、Markdown文件（MD）
                <br />
                文件大小限制：最大2万字
              </>
            }
            accept=".doc,.docx,.txt,.pdf,.md,.markdown"
            onUpload={handleFileUpload}
            disabled={isUploading || isGenerating}
            isUploading={isUploading}
            uploadingText="正在上传文档，请稍候..."
            multiple={false}
          />

          {/* 上传文件信息 */}
          {uploadedFile && (
            <div className={styles.uploadedFileInfo}>
              <div className={styles.fileInfoTitle}>已上传文件</div>
              <div className={styles.fileInfoItem}>
                <div className={styles.fileInfoIcon}>
                  <FileTextOutlined />
                </div>
                <div className={styles.fileInfoDetails}>
                  <div className={styles.fileName}>{uploadedFile.name}</div>
                  <div className={styles.fileSize}>
                    {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* PPT模板选择区域 */}
          <div className={styles.templateContainer}>
            <div className={styles.templateTitle}>选择PPT模板</div>
            <div className={styles.templateDesc}>
              选择适合的学术PPT模板，系统将根据您的文档内容自动生成演示文稿
            </div>
            
            <div className={styles.templateList}>
              {mockPptTemplates.map((template) => (
                <Card
                  key={template.id}
                  className={`${styles.templateCard} ${
                    selectedTemplate?.id === template.id ? styles.templateCardSelected : ''
                  }`}
                  hoverable
                  onClick={() => handleTemplateSelect(template)}
                  cover={
                    <div className={styles.templateThumbnail}>
                      <img src={template.thumbnail} alt={template.name} />
                    </div>
                  }
                >
                  <Card.Meta
                    title={template.name}
                    description={template.description}
                  />
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧：PPT预览和操作 */}
        <div className={styles.pptMain}>
          <div className={styles.mainContainer}>
            <div className={styles.mainHeader}>
              <div className={styles.mainHeaderInfo}>
                <div className={styles.mainHeaderTitle}>PPT预览</div>
                <div className={styles.mainHeaderDesc}>查看生成的PPT文件信息和预览</div>
              </div>
              
              {generateResult && (
                <Button
                  className={styles.regenerateButton}
                  icon={<SyncOutlined />}
                  onClick={handleRegenerate}
                  loading={isGenerating}
                >
                  重新生成
                </Button>
              )}
            </div>

            {/* 动态内容区域 */}
            <div className={styles.mainContent}>
              {isGenerating ? (
                <div className={styles.generatingContainer}>
                  <LoadingOutlined className={styles.generatingIcon} />
                  <div className={styles.generatingTitle}>正在生成PPT...</div>
                  <div className={styles.generatingDesc}>
                    AI正在分析您的文档内容并生成专业的PPT演示文稿，请稍候
                  </div>
                </div>
              ) : generateResult ? (
                <div className={styles.resultContainer}>
                  <div className={styles.resultHeader}>
                    <div className={styles.resultTitle}>生成完成</div>
                    <div className={styles.resultMessage}>{generateResult.message}</div>
                  </div>
                  
                  <div className={styles.resultInfo}>
                    <div className={styles.resultInfoItem}>
                      <span className={styles.resultInfoLabel}>文件名：</span>
                      <span className={styles.resultInfoValue}>{generateResult.filename}</span>
                    </div>
                    <div className={styles.resultInfoItem}>
                      <span className={styles.resultInfoLabel}>文件大小：</span>
                      <span className={styles.resultInfoValue}>
                        {(generateResult.size / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                  </div>

                  {generateResult.document_content && (
                    <div className={styles.documentPreview}>
                      <div className={styles.previewTitle}>文档内容预览</div>
                      <div className={styles.previewContent}>
                        {generateResult.document_content}
                      </div>
                    </div>
                  )}

                  <div className={styles.actionButtons}>
                    <Button
                      type="primary"
                      size="large"
                      icon={<DownloadOutlined />}
                      onClick={handleDownload}
                      className={styles.downloadButton}
                    >
                      下载PPT
                    </Button>
                  </div>
                </div>
              ) : (
                <div className={styles.emptyContainer}>
                  <div className={styles.emptyIcon}>
                    <PlayCircleOutlined />
                  </div>
                  <div className={styles.emptyTitle}>准备生成PPT</div>
                  <div className={styles.emptyDesc}>
                    请上传文档文件并选择PPT模板，然后点击生成按钮
                  </div>
                  
                  {uploadedFile && selectedTemplate && (
                    <Button
                      type="primary"
                      size="large"
                      icon={<PlayCircleOutlined />}
                      onClick={handleGeneratePpt}
                      loading={isGenerating}
                      className={styles.generateButton}
                    >
                      生成PPT
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PptGenerator;
