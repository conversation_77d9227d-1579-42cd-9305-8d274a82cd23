/* AI PPT生成器页面样式 */

/* 页面容器 */
.pptContainer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #EEF3FF;
  padding: 20px;
}

/* 顶部标题 */
.pptTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: fit-content;
  margin-bottom: 16px;
}

.titleLeft {
  display: flex;
  flex-direction: column;
  min-height: fit-content;
}

.titleText {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.titleDesc {
  font-size: 16px;
  color: #555;
}

/* 内容区域 */
.pptContent {
  display: flex;
  flex-direction: row;
  gap: 16px;
  width: 100%;
  height: calc(100vh - 215px);
}

.pptLeft {
  width: 30%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pptMain {
  width: 70%;
  height: 100%;
}

/* 上传文件信息 */
.uploadedFileInfo {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.fileInfoTitle {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.fileInfoItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.fileInfoIcon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: #dbeafe;
  color: #2563eb;
  font-size: 16px;
}

.fileInfoDetails {
  flex: 1;
}

.fileName {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.fileSize {
  font-size: 12px;
  color: #666;
}

/* PPT模板选择区域 */
.templateContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  flex: 1;
  overflow: hidden;
}

.templateTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.templateDesc {
  font-size: 14px;
  color: #555;
  margin-bottom: 16px;
}

.templateList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  flex: 1;
}

.templateCard {
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.templateCard:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.templateCardSelected {
  border-color: #1890ff !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25) !important;
}

.templateThumbnail {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  overflow: hidden;
}

.templateThumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 右侧主内容区域 */
.mainContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.mainHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: fit-content;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 16px;
  margin-bottom: 20px;
}

.mainHeaderInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mainHeaderTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.mainHeaderDesc {
  font-size: 14px;
  color: #666;
}

.regenerateButton {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
}

.mainContent {
  flex: 1;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 生成中状态 */
.generatingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
}

.generatingIcon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.generatingTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.generatingDesc {
  font-size: 14px;
  color: #666;
  max-width: 400px;
}

/* 生成结果 */
.resultContainer {
  width: 100%;
  padding: 20px;
}

.resultHeader {
  text-align: center;
  margin-bottom: 24px;
}

.resultTitle {
  font-size: 20px;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 8px;
}

.resultMessage {
  font-size: 14px;
  color: #666;
}

.resultInfo {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.resultInfoItem {
  display: flex;
  margin-bottom: 8px;
}

.resultInfoItem:last-child {
  margin-bottom: 0;
}

.resultInfoLabel {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.resultInfoValue {
  color: #666;
}

.documentPreview {
  margin-bottom: 24px;
}

.previewTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.previewContent {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  max-height: 200px;
  overflow-y: auto;
}

.actionButtons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.downloadButton {
  height: 44px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
}

/* 空状态 */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
}

.emptyIcon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: #f5f5f5;
  font-size: 40px;
  color: #999;
  margin-bottom: 20px;
}

.emptyTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.emptyDesc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  max-width: 400px;
}

.generateButton {
  height: 44px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pptContent {
    flex-direction: column;
  }
  
  .pptLeft,
  .pptMain {
    width: 100%;
  }
  
  .pptLeft {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .pptContainer {
    padding: 16px;
  }
  
  .titleText {
    font-size: 20px;
  }
  
  .titleDesc {
    font-size: 14px;
  }
  
  .templateContainer,
  .mainContainer {
    padding: 16px;
  }
  
  .templateList {
    gap: 8px;
  }
  
  .templateThumbnail {
    height: 100px;
  }
}
