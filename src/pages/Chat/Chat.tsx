import { getFileTypeForDify } from "@/utils";
import { aiChat<PERSON>pi } from "@/utils/api_chat";
import { sendDifyMessage } from "@/utils/send_dify_message";
import {
    CloseOutlined,
    CloudUploadOutlined,
    FileTextOutlined,
    PlusOutlined,
    SearchOutlined,
    SendOutlined
} from "@ant-design/icons";
import { App, Button, Input, Upload } from "antd";
import React, { useEffect, useRef, useState } from "react";
import styles from "./Chat.module.css";
import MessageItem from "./MessageItem";

interface ChatMessage {
  id: string;
  content: string;
  type: "question" | "answer";
  timestamp: string;
  /** 是否正在流式更新 */
  isStreaming?: boolean;
}

interface DifyFile {
  created_at: number;
  created_by: string;
  extension: string;
  id: string;
  mime_type: string;
  name: string;
  size: number;
}

interface UploadedFile extends DifyFile {
  file_name: string;
  category: string;
}

/** 生成当前时间戳字符串 */
const generateTimestamp = (): string => {
  return new Date().toLocaleTimeString("zh-CN", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });
};

const Chat: React.FC = () => {
  const { message } = App.useApp();
  const [isUploading, setIsUploading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [streamContent, setStreamContent] = useState("");
  const [conversationId, setConversationId] = useState<string>("");
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<string>("");

  const [showHistory, setShowHistory] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [messageList, setMessageList] = useState<ChatMessage[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock历史对话数据
  const historyList = [
    {
      id: "1",
      title: "新对话",
      time: "今天",
      messageCount: 0,
    },
    {
      id: "2",
      title: "数学解题助手",
      time: "今天",
      messageCount: 5,
    },
    {
      id: "3",
      title: "编程学习",
      time: "今天",
      messageCount: 12,
    },
  ];

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // 当消息列表或流式内容更新时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messageList, streamContent]);

  // 处理发送消息
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    console.log("handleSendMessage", inputValue);

    // 1. 创建用户消息并立即插入列表
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      type: "question",
      timestamp: generateTimestamp(),
    };

    setMessageList(prev => [...prev, userMessage]);

    // 2. 清空输入框和上传文件
    const questionText = inputValue.trim();
    const files = uploadedFiles.map(file => ({
      type: file.category,
      transfer_method: "local_file",
      upload_file_id: file.id,
    }));

    setInputValue("");
    setUploadedFiles([]); // 清空上传的文件

    // 3. 创建AI回复消息（用于流式更新）
    const aiMessageId = (Date.now() + 1).toString();
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      content: "",
      type: "answer",
      timestamp: generateTimestamp(),
      isStreaming: true,
    };

    setMessageList(prev => [...prev, aiMessage]);
    setCurrentStreamingMessageId(aiMessageId);
    setIsStreaming(true);
    setStreamContent("");

    // 4. 发起流式请求
    const controller = sendDifyMessage(
      aiChatApi.getChatUrl(),
      {
        query: questionText,
        conversation_id: conversationId || "",
        files: files,
      },
      {
        onMessage: (chunk, fullContent, event) => {
          // 更新流式内容
          setStreamContent(fullContent);
        },
        onComplete: (finalContent, newConversationId, messageId) => {
          console.log("消息完成:", finalContent);
          console.log("会话ID:", newConversationId);
          console.log("消息ID:", messageId);

          // 5. 完成时更新消息列表，移除流式状态
          setMessageList(prev =>
            prev.map(msg =>
              msg.id === aiMessageId ? { ...msg, content: finalContent, isStreaming: false } : msg,
            ),
          );

          // 6. 清理状态
          setConversationId(newConversationId || "");
          setIsStreaming(false);
          setStreamContent("");
          setCurrentStreamingMessageId("");
        },
        onError: (error, errorEvent) => {
          console.error("发生错误:", error, errorEvent);

          // 错误时更新消息为错误状态
          setMessageList(prev =>
            prev.map(msg =>
              msg.id === aiMessageId
                ? { ...msg, content: "抱歉，回复时发生了错误，请重试。", isStreaming: false }
                : msg,
            ),
          );

          // 清理状态
          setIsStreaming(false);
          setStreamContent("");
          setCurrentStreamingMessageId("");
        },
      },
    );
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    console.log("handleSearch", value);
  };

  // 处理新建对话
  const handleNewChat = () => {
    console.log("handleNewChat");
  };

  /** 文件上传 */
  const handleFileUpload = (file: File, fileList: File[]) => {
    if (isUploading) {
      message.warning("文件上传中，请稍后再试");
      return;
    }
    if (isStreaming) {
      message.warning("题目解答中，请稍后再试");
      return;
    }
    setIsUploading(true);

    const formData = new FormData();
    formData.append("file", file);
    console.log("上传文件:", file.name);

    aiChatApi
      .uploadMultipart(formData)
      .then((res: DifyFile) => {
        console.log("文件上传成功:", res.name);
        const result = {
          ...res,
          file_name: file.name,
          category: getFileTypeForDify(file.type || res.mime_type),
        };
        setUploadedFiles(prev => [...prev, result]);
        message.success(`${file.name} 上传成功`);
      })
      .catch(error => {
        console.error("文件上传失败:", error);
        message.error("文件上传失败，请重试");
      })
      .finally(() => {
        setIsUploading(false);
      });

    return false; // 阻止默认上传行为
  };

  // 处理刷新消息
  const handleRefreshMessage = (messageId: string) => {
    console.log("handleRefreshMessage", messageId);
  };

  /** 删除上传的文件 */
  const handleDeleteFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    message.success("文件已删除");
  };

  return (
    <div className={styles.chatContainer}>
      {/* 顶部Header */}
      <div className={styles.chatHeader}>
        {/* <Button
          type="text"
          icon={<HistoryOutlined />}
          className={styles.historyButton}
          onClick={() => setShowHistory(!showHistory)}
        /> */}
        <div className={styles.titleContainer}>
          <div className={styles.titleText}>AI智能对话</div>
          <div className={styles.titleDesc}>支持数学问题、图表生成、代码编程等多种功能</div>
        </div>
      </div>

      {/* 下方内容区域 */}
      <div className={styles.chatContent}>
        {/* 左侧历史对话列表 */}
        <div className={`${styles.historyPanel} ${showHistory ? styles.show : styles.hide}`}>
          <div className={styles.historyHeader}>
            <div className={styles.searchContainer}>
              <Input
                placeholder="搜索对话..."
                prefix={<SearchOutlined />}
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                onPressEnter={e => handleSearch((e.target as HTMLInputElement).value)}
                className={styles.searchInput}
              />
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              className={styles.newChatButton}
              onClick={handleNewChat}
            />
          </div>
          <div className={styles.historyList}>
            {historyList.map(item => (
              <div key={item.id} className={styles.historyItem}>
                <div className={styles.historyTitle}>{item.title}</div>
                <div className={styles.historyMeta}>
                  <span className={styles.historyTime}>{item.time}</span>
                  <span className={styles.historyCount}>{item.messageCount}条对话</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧当前对话面板 */}
        <div className={styles.chatPanel}>
          <div className={styles.messageList}>
            {messageList.map(message => (
              <MessageItem
                key={message.id}
                message={message}
                streamContent={message.id === currentStreamingMessageId ? streamContent : undefined}
                // onRefresh={handleRefreshMessage}
              />
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* 消息输入框 */}
          <div className={styles.inputContainer}>
            {uploadedFiles.length > 0 && (
              <div className={styles.uploadedFileList}>
                {uploadedFiles.map(file => (
                  <div key={file.id} className={styles.uploadedFileItem}>
                    <div className={styles.uploadedFileIcon}>
                      <FileTextOutlined />
                    </div>
                    <div className={styles.uploadedFileName}>{file.file_name}</div>
                    <Button
                      type="text"
                      size="small"
                      icon={<CloseOutlined />}
                      className={styles.deleteFileButton}
                      onClick={() => handleDeleteFile(file.id)}
                      disabled={isUploading || isStreaming}
                    />
                  </div>
                ))}
              </div>
            )}
            <div className={styles.inputContent}>
              <Upload
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.bmp,.tiff,.webp"
                beforeUpload={(file, fileList) => {
                  return handleFileUpload(file, fileList);
                }}
                showUploadList={false}
                multiple={false}
                disabled={isUploading || isStreaming}
              >
                <Button
                  type="text"
                  icon={<CloudUploadOutlined />}
                  className={styles.uploadButton}
                  disabled={isUploading || isStreaming}
                />
              </Upload>

              <Input
                placeholder="输入你的问题，支持数学公式、图表生成、代码识别..."
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                onPressEnter={handleSendMessage}
                className={styles.messageInput}
                maxLength={1000}
                disabled={isStreaming}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                className={styles.sendButton}
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isStreaming}
                loading={isStreaming}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chat;
