import { projectConfigApi } from '@/utils/api_report_config';
import { App, Button, Card, Input, Popconfirm, Spin, Tag, Tooltip } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { ProjectStatus, type ProjectConfig } from '@/types/ReportConfig';
import { CloseOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './History.module.css';

// 状态颜色映射
const statusColorMap: { [key: string]: string } = {
  [ProjectStatus.CONFIGURING]: 'default',
  [ProjectStatus.OUTLINE_GENERATING]: 'processing',
  [ProjectStatus.OUTLINE_GENERATED]: 'processing',
  [ProjectStatus.OUTLINE_FAILED]: 'error',
  [ProjectStatus.OUTLINE_CANCELED]: 'warning',
  [ProjectStatus.REPORT_GENERATING]: 'processing',
  [ProjectStatus.REPORT_GENERATED]: 'success',
  [ProjectStatus.REPORT_FAILED]: 'error',
  [ProjectStatus.REPORT_CANCELED]: 'warning',
};

// 状态文本映射
const statusTextMap: { [key: string]: string } = {
  [ProjectStatus.CONFIGURING]: '配置中',
  [ProjectStatus.OUTLINE_GENERATING]: '大纲生成中',
  [ProjectStatus.OUTLINE_GENERATED]: '大纲已生成',
  [ProjectStatus.OUTLINE_FAILED]: '大纲生成失败',
  [ProjectStatus.OUTLINE_CANCELED]: '大纲生成取消',
  [ProjectStatus.REPORT_GENERATING]: '报告生成中',
  [ProjectStatus.REPORT_GENERATED]: '报告生成完成',
  [ProjectStatus.REPORT_FAILED]: '报告生成失败',
  [ProjectStatus.REPORT_CANCELED]: '报告生成取消',
};

interface HistoryPanelProps {
  onClose: () => void;
  shouldRefresh: boolean;
  onSelect: (id: string) => void;
}

const pageSize = 15; // 每页加载数量

const HistoryPanel: React.FC<HistoryPanelProps> = ({ onClose, shouldRefresh, onSelect }) => {
  const { message } = App.useApp();
  const [historyList, setHistoryList] = useState<ProjectConfig[]>([]);

  // 加载状态
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // 分页状态
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // 无限滚动容器引用
  const listContainerRef = useRef<HTMLDivElement>(null);

  const fetchHistory = useCallback(async (isLoadMore = false) => {
    // 使用函数式更新来获取最新的页码
    setPage(currentPage => {
      const pageToFetch = isLoadMore ? currentPage : 1;

      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        // 重置列表和分页状态
        setHistoryList([]);
        setHasMore(true);
      }

      projectConfigApi
        .getProjectListPaginated(pageToFetch, pageSize)
        .then(res => {
          const items = res.items || [];
          // console.log(items);
          if (isLoadMore) {
            setHistoryList(prev => [...prev, ...items]);
          } else {
            setHistoryList(items);
          }

          if (items.length < pageSize) {
            setHasMore(false);
          }
        })
        .catch(error => {
          console.error('获取历史记录失败:', error);
        })
        .finally(() => {
          setLoading(false);
          setLoadingMore(false);
        });

      // 返回下一页的页码
      return isLoadMore ? currentPage + 1 : 2;
    });
  }, []);

  // 外部触发刷新
  useEffect(() => {
    if (shouldRefresh) {
      fetchHistory();
    }
  }, [shouldRefresh, fetchHistory]);

  // 无限滚动监听
  useEffect(() => {
    const container = listContainerRef.current;
    const handleScroll = () => {
      if (!container || loadingMore || !hasMore) return;
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollHeight - scrollTop - clientHeight < 100) {
        fetchHistory(true);
      }
    };
    container?.addEventListener('scroll', handleScroll);
    return () => container?.removeEventListener('scroll', handleScroll);
  }, [loadingMore, hasMore, fetchHistory]);

  // 删除报告
  const handleDelete = async (projectId: string) => {
    try {
      await projectConfigApi.deleteProjectConfig(projectId);
      message.success('删除成功');
      fetchHistory(); // 删除后刷新列表
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  return (
    <div className={styles.historyPanel}>
      <div className={styles.historyHeader}>
        <div className={styles.headerTitle}>
          历史记录
        </div>
        <Button 
          type="text" 
          icon={<CloseOutlined />} 
          onClick={onClose} 
          className={styles.closeButton}
        />
      </div>
      <div className={styles.toolbar}>
        <Input 
          placeholder="搜索历史记录"
          prefix={<SearchOutlined />}
          className={styles.searchInput}
        />
      </div>
      <div className={styles.historyList} ref={listContainerRef}>
        {loading && historyList.length === 0 ? (
          // 占位卡片
          Array.from({ length: 3 }).map((_, index) => 
            <Card key={index} loading style={{ marginBottom: 12 }} />
          )
        ) : (
          // 实际列表
          historyList.map(item => (
            <div 
              key={item.id} 
              className={styles.historyCard}
              onClick={() => onSelect(item.id)}
            >
              <div className={styles.cardHeader}>
                <div className={styles.cardTitle}>
                  {item.name}
                </div>
                <Tooltip title="删除">
                  <Popconfirm
                    title="确定要删除此报告吗？"
                    onConfirm={e => {
                      e?.stopPropagation();
                      handleDelete(item.id);
                    }}
                    okText="确定"
                    cancelText="取消"
                    onCancel={e => {
                      e?.stopPropagation();
                    }}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      className={styles.deleteButton}
                      onClick={e => {
                        e.stopPropagation();
                      }}
                    />
                  </Popconfirm>
                </Tooltip>
              </div>
              {/* <div className={styles.cardSource}>{item.name || '未知来源'}</div> */}
              <div className={styles.cardFooter}>
                <div className={styles.cardTimestamp}>
                  {dayjs(item.updated_at).format('YYYY/MM/DD HH:mm:ss')}
                </div>
                <Tag color={statusColorMap[item.status] || 'default'}>
                  {statusTextMap[item.status] || item.status}
                </Tag>
              </div>
            </div>
          ))
        )}
        {/* 加载更多指示器 */}
        {loadingMore && (
          <div style={{ textAlign: 'center', padding: '10px' }}>
            <Spin />
          </div>
        )}
        {/* 无更多数据提示 */}
        {!hasMore && historyList.length > 0 && (
          <div style={{ textAlign: 'center', padding: '10px', color: '#999', fontSize: 12 }}>
            已加载全部记录
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryPanel;
