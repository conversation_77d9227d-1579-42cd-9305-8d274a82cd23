import BackgroundLayout from "@/pages/Background/Layout";
import MenuManagement from "@/pages/Background/Menu";
import ModelManagement from "@/pages/Background/Model";
import OrganizationManagement from "@/pages/Background/Organization";
import RoleManagement from "@/pages/Background/Role";
import UsersManagement from "@/pages/Background/Users";
import ComingSoon from "@/pages/ComingSoon";
import Home from "@/pages/Home";
import Login from "@/pages/Login";
import React from "react";
import { Navigate, RouteObject } from "react-router-dom";
import ErrorPage from "./components/ErrorPage";
import ProtectedRoute from "./components/ProtectedRoute";
import AuthRedirect from "./pages/AuthRedirect";
import Chat from "./pages/Chat/Chat";
import Homework from "./pages/Homework/Homework";
import AiTraces from "./pages/AiTraces/AiTraces";
import NotFound from "./pages/NotFound";
import Paper from "./pages/Paper/Paper";

// 创建一个包装函数，方便使用ProtectedRoute
const withPermission = (element: React.ReactNode, menuPath: string) => {
  return <ProtectedRoute menuPath={menuPath}>{element}</ProtectedRoute>;
};

export const moduleRoutes: RouteObject[] = [
  {
    path: "/",
    element: <Navigate to="/college/home" replace />,
  },
  {
    path: "/college",
    children: [
      { path: "home", element: withPermission(<Home />, "/college/home") },
      { path: "homework", element: withPermission(<Homework />, "/college/homework") },
      { path: "chat", element: withPermission(<Chat />, "/college/chat") },
      { path: "paper", element: withPermission(<Paper />, "/college/paper") },
      { path: "ai-traces", element: withPermission(<AiTraces />, "/college/ai-traces") },
      { path: "learning", element: withPermission(<ComingSoon />, "/college/learning") },
    ],
  },
  // 后台管理路由
  {
    path: "/background",
    element: <BackgroundLayout />,
    children: [
      { 
        path: "organization", 
        element: withPermission(<OrganizationManagement />, "/background/organization") 
      },
      { 
        path: "user", 
        element: withPermission(<UsersManagement />, "/background/user") 
      },
      { 
        path: "menu", 
        element: withPermission(<MenuManagement />, "/background/menu") 
      },
      { 
        path: "role", 
        element: withPermission(<RoleManagement />, "/background/role") 
      },
      { 
        path: "model", 
        element: withPermission(<ModelManagement />, "/background/model") 
      },
    ],
  },
];

// 错误页面路由
export const errorRoutes: RouteObject[] = [
  {
    path: "/error/400",
    element: <ErrorPage status="400" />,
  },
  {
    path: "/error/403",
    element: <ErrorPage status="403" />,
  },
  {
    path: "/error/404",
    element: <ErrorPage status="404" />,
  },
  {
    path: "/error/500",
    element: <ErrorPage status="500" />,
  },
  {
    path: "/error",
    element: <ErrorPage />,
  },
];
export const routes: RouteObject[] = [
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "/auth/redirect", 
    element: <AuthRedirect />,
  },
  {
    path: "/soon",
    element: <ComingSoon />,
  },
  {
    path: "/404",
    element: <NotFound />,
  },
  {
    path: "/403",
    element: <ComingSoon 
      title="未授权访问" 
      subTitle="很抱歉，您没有权限访问此页面" 
      status="403" 
    />,
  },
  ...moduleRoutes,
  ...errorRoutes,
];
