# 项目目录结构

## 1. 项目概述
college-agent-frontend - 高校智能助手前端应用，基于React + TypeScript + Vite构建

## 2. 核心目录结构

```
college-agent-frontend/
├── README.md                  # 项目说明文档
├── directories.md             # 目录结构文档
├── index.html                 # HTML入口文件
├── package-lock.json          # 包依赖锁定文件
├── package.json               # 项目配置和依赖管理
├── .env                       # 环境变量配置
├── vite.config.ts             # Vite构建配置
├── tsconfig.json              # TypeScript配置
├── tsconfig.node.json         # Node环境TypeScript配置
├── Dockerfile                 # Docker构建文件
├── nginx.conf                 # Nginx配置文件
├── build.sh                   # 构建脚本
├── wrap.sh                    # 包装脚本
├── public                     # 静态资源目录
├── src                        # 源代码目录
│   ├── App.tsx                # 应用主组件
│   ├── main.tsx               # 应用入口文件
│   ├── routes.tsx             # 路由配置
│   ├── vite-env.d.ts          # Vite环境类型声明
│   ├── assets                 # 资源文件目录
│   ├── components             # 通用组件目录
│   │   ├── ErrorPage.tsx              # 错误页面组件
│   │   ├── LoadingDots.tsx            # 加载动画组件
│   │   ├── MarkdownViewer.tsx         # Markdown查看器组件
│   │   ├── ModelConfigDrawer.tsx      # 模型配置抽屉组件
│   │   ├── Navigator.tsx              # 导航组件
│   │   ├── ProtectedRoute.tsx         # 路由保护组件
│   │   ├── UserInfoDrawer.tsx         # 用户信息抽屉组件
│   │   ├── FormField/                 # 表单字段组件
│   │   ├── LimitedModal/              # 限制型模态框组件
│   │   ├── MarkdownEditor/            # Markdown编辑器组件
│   │   └── TextSelectionTool/         # 文本选择工具组件
│   ├── config                  # 配置文件目录
│   │   ├── menu.tsx            # 菜单配置
│   │   └── theme.ts            # 主题配置
│   ├── contexts                # React上下文目录
│   │   ├── AuthContext.tsx     # 认证上下文
│   │   └── PermissionContext.tsx # 权限上下文
│   ├── hooks                   # 自定义钩子目录
│   ├── pages                   # 页面组件目录
│   │   ├── AiTraces/                  # AI去痕功能页面
│   │   │   ├── AiTraces.tsx           # AI去痕主组件
│   │   │   └── AiTraces.module.css    # AI去痕页面样式
│   │   ├── AuthRedirect.tsx           # 认证重定向页面
│   │   ├── Background/                # 后台管理页面
│   │   │   ├── Layout.tsx             # 后台布局组件
│   │   │   ├── Menu/                  # 菜单管理
│   │   │   ├── Model/                 # 模型管理
│   │   │   ├── Organization/          # 组织管理
│   │   │   ├── Role/                  # 角色管理
│   │   │   └── Users/                 # 用户管理
│   │   ├── Chat/                      # 聊天功能页面
│   │   │   ├── Chat.tsx               # 聊天主组件
│   │   │   ├── Chat.module.css        # 聊天页面样式
│   │   │   └── MessageItem.tsx        # 消息项组件
│   │   ├── ComingSoon.tsx             # 即将推出页面
│   │   ├── Home.tsx                   # 首页
│   │   ├── Homework/                  # 作业助手页面
│   │   │   ├── Homework.tsx           # 作业助手主组件
│   │   │   └── Homework.module.css    # 作业助手页面样式
│   │   ├── Login.tsx                  # 登录页面
│   │   ├── NotFound.tsx               # 404页面
│   │   ├── Paper/                     # 论文相关页面
│   │   │   ├── Paper.tsx              # 论文主组件
│   │   │   ├── Config.tsx             # 论文配置组件
│   │   │   └── Workflow.tsx           # 工作流组件
│   │   └── PaperT/                    # 论文模板页面
│   ├── styles                  # 样式文件目录
│   │   ├── global.css          # 全局样式
│   │   ├── Home.css           # 首页样式
│   │   ├── Navigator.css      # 导航样式
│   │   └── Reports.css        # 报告样式
│   ├── types                   # 类型定义目录
│   │   ├── ApiRes.ts           # API响应类型定义
│   │   ├── Dictionary.ts       # 字典类型
│   │   ├── Menu.ts            # 菜单类型
│   │   ├── ModelConfig.ts     # 模型配置类型
│   │   ├── Organization.ts    # 组织类型
│   │   ├── Page.ts            # 页面类型
│   │   ├── PaperConfig.ts     # 论文配置类型
│   │   ├── ReportConfig.ts    # 报告配置类型
│   │   ├── Role.ts            # 角色类型
│   │   ├── UserInfo.ts        # 用户信息类型
│   │   └── index.ts           # 类型索引
│   └── utils                   # 工具函数目录
│       ├── api.ts              # API请求封装
│       ├── api_report.ts       # 报告API请求
│       ├── api_report_config.ts # 报告配置API请求
│       ├── auth.ts             # 认证工具
│       ├── crypto.ts           # 加密工具
│       ├── dictionary_api.ts   # 字典API
│       ├── fetch_sse.ts        # SSE请求工具
│       └── index.ts            # 工具函数索引
└── test-results                # 测试结果目录
```

## 3. 技术栈说明

- **前端框架：** React 18 + TypeScript
- **构建工具：** Vite
- **UI组件库：** Ant Design
- **路由管理：** React Router v6
- **HTTP请求：** Axios
- **样式方案：** CSS Modules + 全局CSS
- **状态管理：** React Context + useState/useEffect

