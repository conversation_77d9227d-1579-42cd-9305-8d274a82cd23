---
description: 
globs: 
alwaysApply: true
---
college-agent-frontend
├── README.md                  # 项目说明文档
├── directories.md             # 目录结构文档
├── index.html                 # HTML入口文件
├── package-lock.json          # 包依赖锁定文件
├── package.json               # 项目配置和依赖管理
├── .env                       # 环境变量配置
├── vite.config.ts             # Vite构建配置
├── tsconfig.json              # TypeScript配置
├── tsconfig.node.json         # Node环境TypeScript配置
├── Dockerfile                 # Docker构建文件
├── nginx.conf                 # Nginx配置文件
├── build.sh                   # 构建脚本
├── wrap.sh                    # 包装脚本
├── public                     # 静态资源目录
├── src                        # 源代码目录
│   ├── App.tsx                # 应用主组件
│   ├── main.tsx               # 应用入口文件
│   ├── routes.tsx             # 路由配置
│   ├── vite-env.d.ts          # Vite环境类型声明
│   ├── assets                 # 资源文件目录
│   ├── components             # 通用组件目录
│   │   ├── LoadingDots.tsx            # 加载动画组件
│   │   ├── MarkdownViewer.tsx         # Markdown查看器组件
│   │   ├── ModelConfigDrawer.tsx      # 模型配置抽屉组件
│   │   ├── Navigator.tsx              # 导航组件
│   │   ├── OrganizationManagementDrawer.tsx # 组织管理抽屉组件
│   │   ├── ProtectedRoute.tsx         # 路由保护组件
│   │   ├── UserInfoDrawer.tsx         # 用户信息抽屉组件
│   │   ├── UserManagementDrawer.tsx   # 用户管理抽屉组件
│   │   ├── FormField/                 # 表单字段组件
│   │   ├── LimitedModal/              # 限制型模态框组件
│   │   ├── MarkdownEditor/            # Markdown编辑器组件
│   │   ├── ReportConfigPanel/         # 报告配置面板组件
│   │   ├── ReportGuideTool/           # 报告引导工具组件
│   │   ├── TextSelectionTool/         # 文本选择工具组件
│   │   └── WorkflowPanel/             # 工作流面板组件
│   ├── config                  # 配置文件目录
│   ├── contexts                # React上下文目录
│   ├── hooks                   # 自定义钩子目录
│   ├── pages                   # 页面组件目录
│   ├── styles                  # 样式文件目录
│   ├── types                   # 类型定义目录
│   │   └── ApiRes.ts           # API响应类型定义
│   └── utils                   # 工具函数目录
│       ├── api.ts              # API请求封装
│       ├── api_report.ts       # 报告API请求
│       ├── api_report_config.ts # 报告配置API请求
│       ├── auth.ts             # 认证工具
│       ├── crypto.ts           # 加密工具
│       ├── dictionary_api.ts   # 字典API
│       ├── fetch_sse.ts        # SSE请求工具
│       └── index.ts            # 工具函数索引
├── test-results                # 测试结果目录
└── tests                       # 测试文件目录

2. 模块划分与依赖关系
核心模块及职责
components: 提供可复用UI组件
  - FormField: 表单字段组件
  - MarkdownEditor: Markdown编辑器
  - ReportConfigPanel: 报告配置面板
  - TextSelectionTool: 文本选择工具
  - WorkflowPanel: 工作流面板
  - LoadingDots: 加载状态指示器
  - MarkdownViewer: Markdown内容查看器
  - NavigationBar: 导航栏
  - ProtectedRoute: 权限路由保护
pages: 页面级组件
contexts: 提供React上下文
hooks: 复用业务逻辑
utils: 工具函数
  - api.ts: 统一API调用封装与拦截器
  - auth.ts: 认证和权限管理
  - api_report.ts: 报告相关API
  - api_report_config.ts: 报告配置API
  - crypto.ts: 加密相关功能
  - fetch_sse.ts: 服务器发送事件(SSE)请求处理

主要依赖库
React + React DOM: 前端核心框架
React Router: 路由管理
Ant Design: UI组件库
Axios: HTTP请求
TypeScript: 类型支持

3. 入口文件分析
main.tsx 作为应用入口点:
  - 初始化React应用
  - 配置路由系统
  - 渲染根组件到DOM
App.tsx 结构:
  - 包含路由定义和布局结构
  - 集成认证逻辑
  - 错误处理

4. API请求拦截器系统
  - 请求拦截器: 添加授权token
  - 响应拦截器: 统一错误处理和响应格式
  - API响应格式: {code, data, error, success}
  - 认证失效处理: 401状态码处理和登录重定向