---
description: 
globs: 
alwaysApply: true
---
# API拦截器规则与标准

## 1. API响应格式规范

所有后端API响应必须遵循统一的格式规范，便于前端统一处理：

```typescript
interface ApiResponse<T = any> {
    code: number;       // 状态码，0表示成功，其他表示错误
    data: T;            // 业务数据，成功时返回
    error: string;      // 错误信息，失败时返回
    success: boolean;   // 是否成功，true/false
}
```

## 2. 请求拦截器规则

所有通过axios发出的请求必须经过以下拦截处理：

- **授权令牌注入**：所有需要认证的请求自动注入Bearer Token
- **请求头标准化**：根据不同请求类型设置合适的Content-Type
- **超时设置**：根据不同API设置合理的超时时间
- **错误日志**：请求错误时进行详细日志记录

```typescript
// 请求拦截器实现示例
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 1. 从AuthManager获取并注入Token
    const token = AuthManager.getToken();
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 2. 记录请求日志
    console.log(`请求URL: ${config.url}`, config);
    
    return config;
  },
  (error: any) => {
    // 统一处理请求错误
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);
```

## 3. 响应拦截器规则

所有从后端接收的响应必须经过以下处理：

- **数据标准化**：将所有响应转换为标准ApiResponse格式
- **错误码统一处理**：根据不同错误码执行相应操作
- **认证失效处理**：401状态码处理，自动跳转登录页
- **业务错误处理**：显示友好错误信息，并拒绝Promise
- **网络错误处理**：连接超时、网络异常等情况处理
- **响应日志**：记录所有响应数据，支持开发调试

```typescript
// 响应拦截器实现示例
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 统一处理响应格式
    const res = response.data as ApiResponse;
    
    // 1. 处理认证失效 (401)
    if (res.code === 401) {
      message.error('登录无效或已过期，请重新登录');
      // 清除登录信息并跳转登录页
      AuthManager.clear();
      window.location.href = `${getBasePath()}/login`;
      return Promise.reject(new Error(res.error || '登录无效或已过期'));
    }
    
    // 2. 标准格式转换
    if (res.code === undefined) {
      return {
        code: response.status,
        data: response.data,
        success: response.status >= 200 && response.status < 300,
      };
    }

    // 3. 处理业务错误
    if (!res.success || res.code !== 0) {
      message.error(res.error || '操作失败');
      return Promise.reject(new Error(res.error || '操作失败'));
    }

    // 4. 返回标准化数据
    return res.data;
  },
  (error: any) => {
    // 处理HTTP错误
    if (error.response?.status === 401) {
      message.error('登录无效或已过期，请重新登录');
      AuthManager.clear();
      window.location.href = `${getBasePath()}/login`;
    } else {
      const errorResponse: ApiResponse = {
        code: error.response?.status || 500,
        data: null,
        error: error.response?.data?.error || '服务器错误，请检查后重试',
        success: false
      };
      message.error(errorResponse.error);
    }
    return Promise.reject(error);
  }
);
```

## 4. API请求分组管理

所有API请求应按业务域进行分组管理，每个分组独立维护在单独的文件中：

- **auth**: 认证相关API - 登录、注册、用户信息
- **report**: 报告相关API - 创建、查询、删除报告
- **model**: 模型配置相关API

每个API定义必须包含：
- 清晰的方法命名
- 完整的TypeScript类型注解（请求参数和响应类型）
- 中文注释说明用途

示例：
```typescript
// 报告相关API
export const reportApi = {
  // 创建报告
  createReport: (data: CreateReportParams): Promise<Report> =>
    api.post('/reports/', data),

  // 获取报告列表
  getReports: (): Promise<{ reports: Report[] }> =>
    api.get('/reports/'),
};
```

## 5. 错误处理与日志

- **全局错误处理**：未被拦截器捕获的错误应通过全局错误边界处理
- **详细日志记录**：所有API请求必须记录详细的请求/响应日志
- **用户友好提示**：所有错误必须转换为用户可理解的提示信息
- **错误上报**：关键错误应考虑上报至错误监控系统

